<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Location;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Models\MineMark;
use App\Models\ActiveEffect;
use App\Services\MineDetectionService;

class CheckMineDetection extends Command
{
    protected $signature = 'check:mine-detection';
    protected $description = 'Проверяет систему обнаружения в рудниках для игрока admin';

    public function handle()
    {
        $this->info('🔍 Проверка системы обнаружения в рудниках...');
        $this->newLine();

        // Проверяем игрока admin
        $admin = User::where('name', 'admin')->first();
        if (!$admin) {
            $this->error("❌ Игрок admin не найден!");
            return 1;
        }
        $this->info("✅ Игрок admin найден (ID: {$admin->id})");

        // Проверяем локацию
        $location = Location::where('name', 'аааааааааааа')->first();
        if (!$location) {
            $this->error("❌ Локация 'аааааааааааа' не найдена!");
            return 1;
        }
        $this->info("✅ Локация найдена: {$location->name} (ID: {$location->id})");

        // Проверяем рудник в этой локации
        $mineLocation = MineLocation::where('location_id', $location->id)->first();
        if (!$mineLocation) {
            $this->warn("⚠️ Рудник в локации не найден!");
        } else {
            $this->info("✅ Рудник найден: {$mineLocation->name} (ID: {$mineLocation->id})");
        }

        // Проверяем текущую локацию игрока
        $this->info("📍 Текущая локация игрока: {$admin->location}");
        $isInLocation = $admin->location === $location->name;
        $this->info("   Находится в нужной локации: " . ($isInLocation ? '✅ Да' : '❌ Нет'));

        // Проверяем метки обнаружения в рудниках
        $this->newLine();
        $this->info("🔍 Проверка меток обнаружения:");

        if ($mineLocation) {
            // Проверяем через сервис
            $detectionService = app(MineDetectionService::class);
            $isDetected = $detectionService->isPlayerDetected($admin->id, $location->id, $mineLocation->id);
            $this->info("   Игрок обнаружен (через сервис): " . ($isDetected ? '✅ Да' : '❌ Нет'));

            // Проверяем прямо в таблице mine_marks
            $mineMarks = MineMark::where('player_id', $admin->id)
                ->where('location_id', $location->id)
                ->where('mine_location_id', $mineLocation->id)
                ->where('expires_at', '>', now())
                ->get();

            $this->info("   Активных меток в БД: {$mineMarks->count()}");
            foreach ($mineMarks as $mark) {
                $this->info("     - ID: {$mark->id}, истекает: {$mark->expires_at}");
            }
        }

        // Проверяем активные эффекты "Замечен"
        $detectionEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $admin->id)
            ->where('effect_type', 'detected')
            ->where('ends_at', '>', now())
            ->get();

        $this->info("   Активных эффектов 'Замечен': {$detectionEffects->count()}");
        foreach ($detectionEffects as $effect) {
            $this->info("     - ID: {$effect->id}, истекает: {$effect->ends_at}");
        }

        // Проверяем мобов в локации
        $this->newLine();
        $this->info("👹 Проверка мобов в локации:");

        $mobsInLocation = Mob::where('location', $location->name)
            ->orWhere('location_id', $location->id)
            ->get();

        $this->info("   Мобов в локации: {$mobsInLocation->count()}");
        foreach ($mobsInLocation as $mob) {
            $this->info("     - {$mob->name} (ID: {$mob->id}), HP: {$mob->hp}/{$mob->max_hp}");
        }

        // Проверяем последние атаки мобов
        $this->newLine();
        $this->info("⚔️ Проверка последних атак мобов:");
        $this->info("   Логи слишком большие для анализа в памяти");

        return 0;
    }
}
