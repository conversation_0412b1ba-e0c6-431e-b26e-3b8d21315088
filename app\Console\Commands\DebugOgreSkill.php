<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MobSkill;
use App\Models\MobSkillTemplate;
use App\Services\Mine\MobSkillIntegrationService;
use App\Services\MobSkillFramework;

class DebugOgreSkill extends Command
{
    protected $signature = 'debug:ogre-skill';
    protected $description = 'Отладка скилла Огра - детальная диагностика';

    public function handle()
    {
        $this->info('🔍 ДЕТАЛЬНАЯ ОТЛАДКА СКИЛЛА ОГРА');
        $this->info('=' . str_repeat('=', 50));
        $this->newLine();

        // Получаем основные объекты
        $admin = User::where('name', 'admin')->first();
        $ogre = Mob::find(14);
        $template = MobSkillTemplate::where('name', 'Тяжелый удар')->first();

        if (!$admin || !$ogre || !$template) {
            $this->error('❌ Не найдены необходимые объекты!');
            return 1;
        }

        $this->info("✅ Игрок: {$admin->name} (ID: {$admin->id})");
        $this->info("✅ Огр: {$ogre->name} (ID: {$ogre->id})");
        $this->info("✅ Шаблон скилла: {$template->name} (ID: {$template->id})");
        $this->newLine();

        // Проверяем привязку скилла
        $mobSkill = MobSkill::where('mob_id', $ogre->id)
            ->where('skill_template_id', $template->id)
            ->first();

        if (!$mobSkill) {
            $this->error('❌ Скилл не привязан к Огру!');
            return 1;
        }

        $this->info("✅ Скилл привязан к Огру:");
        $this->info("   ID: {$mobSkill->id}");
        $this->info("   Шанс: {$mobSkill->chance}%");
        $this->info("   Кулдаун до: " . ($mobSkill->cooldown_ends_at ?? 'нет'));
        $this->info("   Последнее использование: " . ($mobSkill->last_used_at ?? 'никогда'));
        $this->newLine();

        // Проверяем шаблон скилла
        $this->info("📋 ДЕТАЛИ ШАБЛОНА СКИЛЛА:");
        $this->info("   Название: {$template->name}");
        $this->info("   Тип эффекта: {$template->effect_type}");
        $this->info("   Шанс (шаблон): {$template->chance}%");
        $this->info("   Кулдаун: {$template->cooldown}с");
        $this->info("   Длительность: {$template->duration}с");
        $this->info("   Активен: " . ($template->is_active ? 'Да' : 'Нет'));
        $this->info("   Данные эффекта: " . json_encode($template->effect_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        $this->newLine();

        // Проверяем связи
        $this->info("🔗 ПРОВЕРКА СВЯЗЕЙ:");
        $this->info("   mobSkill->skill: " . ($mobSkill->skill ? "найден (ID: {$mobSkill->skill->id})" : "НЕ НАЙДЕН"));
        $this->info("   mobSkill->skillTemplate: " . ($mobSkill->skillTemplate ? "найден (ID: {$mobSkill->skillTemplate->id})" : "НЕ НАЙДЕН"));
        $this->newLine();

        // Тестируем логику активации
        $this->info("🧪 ТЕСТИРОВАНИЕ ЛОГИКИ АКТИВАЦИИ:");
        
        // 1. Проверяем кулдаун
        $isAvailable = !$mobSkill->cooldown_ends_at || now()->gte($mobSkill->cooldown_ends_at);
        $this->info("   1. Скилл доступен (не на кулдауне): " . ($isAvailable ? '✅ ДА' : '❌ НЕТ'));
        
        if (!$isAvailable) {
            $this->info("      Кулдаун истекает: {$mobSkill->cooldown_ends_at}");
            $this->info("      Текущее время: " . now());
        }

        // 2. Проверяем условия активации
        $this->info("   2. Проверка условий активации...");
        
        // Проверяем, есть ли trigger в effect_data шаблона
        $effectData = $template->effect_data ?? [];
        $trigger = $effectData['trigger'] ?? 'не указан';
        $this->info("      Триггер в шаблоне: {$trigger}");

        // 3. Проверяем шанс
        $chance = $mobSkill->chance ?? $template->chance ?? 0;
        $this->info("   3. Шанс активации: {$chance}%");

        // 4. Симулируем активацию
        $this->info("   4. Симуляция активации...");
        
        $context = [
            'event_type' => 'attack',
            'mine_location_id' => 172,
            'target_type' => 'player',
            'is_mine_mob' => true,
            'damage_dealt' => 50,
            'attack_type' => 'test'
        ];

        $this->info("      Контекст: " . json_encode($context, JSON_UNESCAPED_UNICODE));

        // Тестируем через MobSkillIntegrationService
        $mobSkillService = app(MobSkillIntegrationService::class);
        $activatedSkills = $mobSkillService->processMobAttackSkills($ogre, $admin, $context);

        $this->info("   5. Результат активации:");
        if (empty($activatedSkills)) {
            $this->warn("      ⚠️ Скиллы НЕ активировались");
        } else {
            $this->info("      ✅ Активированы скиллы:");
            foreach ($activatedSkills as $skill) {
                $this->info("         " . json_encode($skill, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            }
        }

        // Тестируем напрямую через MobSkillFramework
        $this->newLine();
        $this->info("🔧 ПРЯМОЕ ТЕСТИРОВАНИЕ ЧЕРЕЗ MobSkillFramework:");
        
        $skillFramework = app(MobSkillFramework::class);
        $directResult = $skillFramework->processMobSkills($ogre, $admin, $context);

        if (empty($directResult)) {
            $this->warn("   ⚠️ Прямой вызов НЕ активировал скиллы");
        } else {
            $this->info("   ✅ Прямой вызов активировал скиллы:");
            foreach ($directResult as $skill) {
                $this->info("      " . json_encode($skill, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            }
        }

        // Проверяем все скиллы Огра
        $this->newLine();
        $this->info("📊 ВСЕ СКИЛЛЫ ОГРА:");
        $allSkills = $ogre->skills()->with('skillTemplate')->get();
        
        foreach ($allSkills as $skill) {
            $this->info("   Скилл ID: {$skill->id}");
            $this->info("   Шаблон: " . ($skill->skillTemplate->name ?? 'НЕТ'));
            $this->info("   Шанс: {$skill->chance}%");
            $this->info("   Кулдаун: " . ($skill->cooldown_ends_at ?? 'нет'));
            $this->info("   ---");
        }

        return 0;
    }
}
