<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\Location;
use App\Models\MobSkillTemplate;
use App\Models\MobSkill;

class CheckOgreStatus extends Command
{
    protected $signature = 'check:ogre-status';
    protected $description = 'Проверяет статус Огра в локации аааааааааааа и его скиллы';

    public function handle()
    {
        $this->info('🔍 Проверка статуса Огра и его скиллов...');
        $this->newLine();

        // Проверяем локацию
        $location = Location::where('name', 'аааааааааааа')->first();
        if ($location) {
            $this->info("✅ Локация найдена: {$location->name} (ID: {$location->id})");
        } else {
            $this->error("❌ Локация 'аааааааааааа' не найдена!");
            return 1;
        }

        // Проверяем всех Огров
        $ogres = Mob::where('name', 'like', '%Огр%')->get();
        $this->info("📊 Найдено Огров: {$ogres->count()}");
        
        foreach ($ogres as $ogre) {
            $this->info("  ID: {$ogre->id}, Имя: {$ogre->name}, Локация: {$ogre->location}, HP: {$ogre->hp}/{$ogre->max_hp}");
        }

        // Ищем Огра в нужной локации
        $targetOgre = $ogres->where('location', 'аааааааааааа')->first();
        if (!$targetOgre) {
            $this->warn("⚠️ Огр в локации 'аааааааааааа' не найден!");
            $this->info("Попробуем найти Огра по другим критериям...");
            
            // Ищем по location_id
            if ($location) {
                $targetOgre = Mob::where('name', 'like', '%Огр%')
                    ->where('location_id', $location->id)
                    ->first();
                    
                if ($targetOgre) {
                    $this->info("✅ Найден Огр по location_id: {$targetOgre->name} (ID: {$targetOgre->id})");
                }
            }
        } else {
            $this->info("✅ Огр в локации найден: {$targetOgre->name} (ID: {$targetOgre->id})");
        }

        // Проверяем шаблон скилла
        $template = MobSkillTemplate::where('name', 'Тяжелый удар')->first();
        if ($template) {
            $this->info("✅ Шаблон скилла 'Тяжелый удар' найден:");
            $this->info("   ID: {$template->id}");
            $this->info("   Шанс: {$template->chance}%");
            $this->info("   Кулдаун: {$template->cooldown}с");
            $this->info("   Длительность: {$template->duration}с");
            $this->info("   Тип эффекта: {$template->effect_type}");
        } else {
            $this->error("❌ Шаблон скилла 'Тяжелый удар' не найден!");
        }

        // Проверяем привязки скиллов
        if ($targetOgre && $template) {
            $mobSkill = MobSkill::where('mob_id', $targetOgre->id)
                ->where('skill_template_id', $template->id)
                ->first();
                
            if ($mobSkill) {
                $this->info("✅ Скилл привязан к Огру:");
                $this->info("   Шанс: {$mobSkill->chance}%");
                $this->info("   Кулдаун до: " . ($mobSkill->cooldown_ends_at ?? 'нет'));
                $this->info("   Последнее использование: " . ($mobSkill->last_used_at ?? 'никогда'));
            } else {
                $this->warn("⚠️ Скилл 'Тяжелый удар' не привязан к Огру!");
            }
        }

        // Проверяем игрока admin
        $admin = User::where('name', 'admin')->first();
        if ($admin) {
            $this->info("✅ Игрок admin найден (ID: {$admin->id})");
        } else {
            $this->error("❌ Игрок admin не найден!");
        }

        return 0;
    }
}
