<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Location;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Models\MobSkill;
use App\Models\MobSkillTemplate;
use App\Models\MineMark;
use App\Models\ActiveEffect;
use App\Services\MineDetectionService;
use App\Services\Mine\MobSkillIntegrationService;
use App\Jobs\MobAutoAttackJob;

class TestRealOgreSkill extends Command
{
    protected $signature = 'test:real-ogre-skill {--setup : Настроить тестовую ситуацию} {--attack : Выполнить реальную атаку} {--cleanup : Очистить тестовые данные}';
    protected $description = 'Тестирует скилл Огра в реальной игровой ситуации';

    public function handle()
    {
        $this->info('🧪 ТЕСТИРОВАНИЕ СКИЛЛА ОГРА В РЕАЛЬНОЙ ИГРОВОЙ СИТУАЦИИ');
        $this->info('=' . str_repeat('=', 60));
        $this->newLine();

        if ($this->option('cleanup')) {
            return $this->cleanup();
        }

        if ($this->option('setup')) {
            return $this->setup();
        }

        if ($this->option('attack')) {
            return $this->executeRealAttack();
        }

        // По умолчанию показываем статус
        return $this->showStatus();
    }

    private function showStatus()
    {
        $this->info('📊 ТЕКУЩИЙ СТАТУС СИСТЕМЫ:');
        $this->newLine();

        // Проверяем основные компоненты
        $admin = User::where('name', 'admin')->first();
        $location = Location::where('name', 'аааааааааааа')->first();
        $mineLocation = MineLocation::where('location_id', $location->id ?? 0)->first();
        $ogre = Mob::find(14);

        $this->info("✅ Игрок admin: " . ($admin ? "найден (ID: {$admin->id})" : "НЕ НАЙДЕН"));
        $this->info("✅ Локация: " . ($location ? "найдена (ID: {$location->id})" : "НЕ НАЙДЕНА"));
        $this->info("✅ Рудник: " . ($mineLocation ? "найден (ID: {$mineLocation->id})" : "НЕ НАЙДЕН"));
        $this->info("✅ Огр: " . ($ogre ? "найден (ID: {$ogre->id})" : "НЕ НАЙДЕН"));

        if ($admin && $location) {
            $currentLocation = $admin->statistics->current_location ?? 'неизвестно';
            $this->info("📍 Локация игрока: {$currentLocation}");
            $this->info("📍 Нужная локация: {$location->name}");
            $isInLocation = $currentLocation === $location->name;
            $this->info("   Игрок в нужной локации: " . ($isInLocation ? '✅ ДА' : '❌ НЕТ'));
        }

        if ($admin && $mineLocation && $location) {
            $detectionService = app(MineDetectionService::class);
            $isDetected = $detectionService->isPlayerDetected($admin->id, $location->id, $mineLocation->id);
            $this->info("🔍 Игрок обнаружен в руднике: " . ($isDetected ? '✅ ДА' : '❌ НЕТ'));
        }

        if ($ogre) {
            $template = MobSkillTemplate::where('name', 'Тяжелый удар')->first();
            $mobSkill = MobSkill::where('mob_id', $ogre->id)
                ->where('skill_template_id', $template->id ?? 0)
                ->first();

            $this->info("⚔️ Скилл привязан к Огру: " . ($mobSkill ? '✅ ДА' : '❌ НЕТ'));
            if ($mobSkill) {
                $this->info("   Шанс активации: {$mobSkill->chance}%");
                $this->info("   Кулдаун до: " . ($mobSkill->cooldown_ends_at ?? 'нет'));
            }
        }

        $this->newLine();
        $this->info('💡 КОМАНДЫ:');
        $this->info('   php artisan test:real-ogre-skill --setup    # Настроить тестовую ситуацию');
        $this->info('   php artisan test:real-ogre-skill --attack   # Выполнить реальную атаку');
        $this->info('   php artisan test:real-ogre-skill --cleanup  # Очистить тестовые данные');

        return 0;
    }

    private function setup()
    {
        $this->info('🔧 НАСТРОЙКА ТЕСТОВОЙ СИТУАЦИИ:');
        $this->newLine();

        // Получаем основные объекты
        $admin = User::where('name', 'admin')->first();
        $location = Location::where('name', 'аааааааааааа')->first();
        $mineLocation = $location ? MineLocation::where('location_id', $location->id)->first() : null;
        $ogre = Mob::find(14);

        if (!$admin || !$location || !$mineLocation || !$ogre) {
            $this->error('❌ Не найдены необходимые объекты для тестирования!');
            return 1;
        }

        // 1. Перемещаем игрока в нужную локацию
        $admin->statistics->current_location = $location->name;
        $admin->statistics->save();
        $this->info("✅ Игрок перемещен в локацию: {$location->name}");

        // 2. Сбрасываем кулдаун скилла Огра
        $template = MobSkillTemplate::where('name', 'Тяжелый удар')->first();
        if ($template) {
            $mobSkill = MobSkill::where('mob_id', $ogre->id)
                ->where('skill_template_id', $template->id)
                ->first();

            if ($mobSkill) {
                $mobSkill->cooldown_ends_at = null;
                $mobSkill->last_used_at = null;
                $mobSkill->chance = 100; // 100% шанс для теста
                $mobSkill->save();
                $this->info("✅ Кулдаун скилла сброшен, шанс установлен на 100%");
            }
        }

        // 3. Создаем метку обнаружения в руднике
        $detectionService = app(MineDetectionService::class);
        $mark = $detectionService->createMark($admin, $mineLocation, 600); // 10 минут
        $this->info("✅ Создана метка обнаружения в руднике (ID: {$mark->id})");

        // 4. Проверяем, что все настроено
        $isDetected = $detectionService->isPlayerDetected($admin->id, $location->id, $mineLocation->id);
        $this->info("🔍 Проверка обнаружения: " . ($isDetected ? '✅ ДА' : '❌ НЕТ'));

        $this->newLine();
        $this->info('🎯 Тестовая ситуация настроена! Теперь можно выполнить атаку:');
        $this->info('   php artisan test:real-ogre-skill --attack');

        return 0;
    }

    private function executeRealAttack()
    {
        $this->info('⚔️ ВЫПОЛНЕНИЕ РЕАЛЬНОЙ АТАКИ:');
        $this->newLine();

        // Получаем основные объекты
        $admin = User::where('name', 'admin')->first();
        $location = Location::where('name', 'аааааааааааа')->first();
        $mineLocation = $location ? MineLocation::where('location_id', $location->id)->first() : null;
        $ogre = Mob::find(14);

        if (!$admin || !$location || !$mineLocation || !$ogre) {
            $this->error('❌ Не найдены необходимые объекты!');
            return 1;
        }

        // Проверяем, что игрок обнаружен
        $detectionService = app(MineDetectionService::class);
        $isDetected = $detectionService->isPlayerDetected($admin->id, $location->id, $mineLocation->id);

        if (!$isDetected) {
            $this->warn('⚠️ Игрок не обнаружен в руднике! Запустите --setup сначала');
            return 1;
        }

        $this->info("✅ Игрок обнаружен в руднике");
        $this->info("✅ Огр готов к атаке: {$ogre->name} (HP: {$ogre->hp}/{$ogre->max_hp})");

        // Проверяем здоровье игрока до атаки
        $healthBefore = $admin->profile->current_hp ?? $admin->profile->max_hp;
        $this->info("💚 Здоровье игрока до атаки: {$healthBefore}");

        // Проверяем активные эффекты до атаки
        $stunEffectsBefore = ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $admin->id)
            ->where('effect_type', 'stun')
            ->where('ends_at', '>', now())
            ->count();
        $this->info("⚡ Эффектов оглушения до атаки: {$stunEffectsBefore}");

        // Выполняем реальную атаку через MobAutoAttackJob
        $this->info("🤖 Запускаем MobAutoAttackJob...");

        try {
            $job = new MobAutoAttackJob();
            $job->handle(
                app(\App\Services\BattleLogService::class),
                app(\App\Services\SkillService::class),
                app(\App\Services\LogFormattingService::class)
            );

            $this->info("✅ MobAutoAttackJob выполнен");
        } catch (\Exception $e) {
            $this->error("❌ Ошибка при выполнении MobAutoAttackJob: " . $e->getMessage());
            return 1;
        }

        // Проверяем результаты
        $admin->refresh();
        $healthAfter = $admin->profile->current_hp ?? $admin->profile->max_hp;
        $this->info("💚 Здоровье игрока после атаки: {$healthAfter}");

        $damageTaken = $healthBefore - $healthAfter;
        if ($damageTaken > 0) {
            $this->info("⚔️ Урон получен: {$damageTaken}");
        } else {
            $this->warn("⚠️ Урон не получен");
        }

        // Проверяем активные эффекты после атаки
        $stunEffectsAfter = ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $admin->id)
            ->where('effect_type', 'stun')
            ->where('ends_at', '>', now())
            ->get();

        $this->info("⚡ Эффектов оглушения после атаки: {$stunEffectsAfter->count()}");

        if ($stunEffectsAfter->count() > 0) {
            $this->info("🎯 СКИЛЛ АКТИВИРОВАЛСЯ! Детали:");
            foreach ($stunEffectsAfter as $effect) {
                $this->info("   - Эффект ID: {$effect->id}");
                $this->info("   - Истекает: {$effect->ends_at}");
                $this->info("   - Данные: " . json_encode($effect->effect_data, JSON_UNESCAPED_UNICODE));
            }
        } else {
            $this->warn("⚠️ Скилл оглушения НЕ активировался");
        }

        return 0;
    }

    private function cleanup()
    {
        $this->info('🧹 ОЧИСТКА ТЕСТОВЫХ ДАННЫХ:');
        $this->newLine();

        $admin = User::where('name', 'admin')->first();
        $location = Location::where('name', 'аааааааааааа')->first();
        $mineLocation = $location ? MineLocation::where('location_id', $location->id)->first() : null;

        if ($admin) {
            // Удаляем метки обнаружения
            if ($location && $mineLocation) {
                MineMark::where('player_id', $admin->id)
                    ->where('location_id', $location->id)
                    ->where('mine_location_id', $mineLocation->id)
                    ->delete();
                $this->info("✅ Удалены метки обнаружения");
            }

            // Удаляем эффекты оглушения
            ActiveEffect::where('target_type', 'App\\Models\\User')
                ->where('target_id', $admin->id)
                ->where('effect_type', 'stun')
                ->delete();
            $this->info("✅ Удалены эффекты оглушения");
        }

        $this->info("🎯 Очистка завершена!");
        return 0;
    }
}
