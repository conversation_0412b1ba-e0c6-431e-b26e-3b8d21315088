<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Location;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Models\MineMark;
use App\Services\MineDetectionService;
use App\Jobs\MobAutoAttackJob;

class DebugMobAutoAttack extends Command
{
    protected $signature = 'debug:mob-auto-attack';
    protected $description = 'Отладка MobAutoAttackJob - почему не атакует Огр';

    public function handle()
    {
        $this->info('🔍 ОТЛАДКА MobAutoAttackJob');
        $this->info('=' . str_repeat('=', 50));
        $this->newLine();

        // Получаем основные объекты
        $admin = User::where('name', 'admin')->first();
        $location = Location::where('name', 'аааааааааааа')->first();
        $mineLocation = $location ? MineLocation::where('location_id', $location->id)->first() : null;
        $ogre = Mob::find(14);

        $this->info("✅ Игрок: {$admin->name} (ID: {$admin->id})");
        $this->info("✅ Локация: {$location->name} (ID: {$location->id})");
        $this->info("✅ Рудник: {$mineLocation->name} (ID: {$mineLocation->id})");
        $this->info("✅ Огр: {$ogre->name} (ID: {$ogre->id})");
        $this->newLine();

        // Проверяем метки обнаружения
        $this->info("🔍 ПРОВЕРКА МЕТОК ОБНАРУЖЕНИЯ:");
        
        $detectionService = app(MineDetectionService::class);
        $isDetected = $detectionService->isPlayerDetected($admin->id, $location->id, $mineLocation->id);
        $this->info("   Игрок обнаружен: " . ($isDetected ? '✅ ДА' : '❌ НЕТ'));

        // Проверяем метки в БД
        $marks = MineMark::where('player_id', $admin->id)
            ->where('location_id', $location->id)
            ->where('mine_location_id', $mineLocation->id)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->get();

        $this->info("   Активных меток в БД: {$marks->count()}");
        foreach ($marks as $mark) {
            $this->info("     - ID: {$mark->id}, истекает: {$mark->expires_at}");
        }

        // Проверяем, что возвращает getDetectedPlayersInMines
        $this->info("   Проверяем getDetectedPlayersInMines...");
        
        // Используем рефлексию для доступа к приватному методу
        $job = new MobAutoAttackJob();
        $reflection = new \ReflectionClass($job);
        $method = $reflection->getMethod('getDetectedPlayersInMines');
        $method->setAccessible(true);
        
        $detectedPlayers = $method->invoke($job, $detectionService);
        $this->info("   Найдено обнаруженных игроков: " . count($detectedPlayers));
        
        foreach ($detectedPlayers as $playerData) {
            $this->info("     - Игрок ID: {$playerData['player_id']}, Рудник ID: {$playerData['mine_location_id']}");
        }

        // Проверяем мобов в локации
        $this->newLine();
        $this->info("👹 ПРОВЕРКА МОБОВ В ЛОКАЦИИ:");
        
        // Проверяем разные способы поиска мобов
        $mobsByLocation = Mob::where('location', $location->name)->get();
        $this->info("   По полю location: {$mobsByLocation->count()}");
        
        $mobsByLocationId = Mob::where('location_id', $location->id)->get();
        $this->info("   По полю location_id: {$mobsByLocationId->count()}");
        
        $mobsByMineLocationId = Mob::where('mine_location_id', $mineLocation->id)->get();
        $this->info("   По полю mine_location_id: {$mobsByMineLocationId->count()}");

        // Проверяем конкретно Огра
        $this->info("   Огр (ID: 14):");
        $this->info("     location: '{$ogre->location}'");
        $this->info("     location_id: " . ($ogre->location_id ?? 'null'));
        $this->info("     mine_location_id: " . ($ogre->mine_location_id ?? 'null'));
        $this->info("     mob_type: '{$ogre->mob_type}'");

        // Проверяем, какие мобы найдет MobAutoAttackJob для этой локации
        $this->newLine();
        $this->info("🎯 СИМУЛЯЦИЯ ПОИСКА МОБОВ В MobAutoAttackJob:");
        
        if (!empty($detectedPlayers)) {
            $playerData = $detectedPlayers[0];
            $this->info("   Для игрока ID: {$playerData['player_id']} в руднике ID: {$playerData['mine_location_id']}");
            
            // Используем рефлексию для доступа к приватному методу
            $getMobsMethod = $reflection->getMethod('getAvailableMobsForMineLocation');
            $getMobsMethod->setAccessible(true);
            
            $mineLocationObj = MineLocation::find($playerData['mine_location_id']);
            $availableMobs = $getMobsMethod->invoke($job, $mineLocationObj);
            
            $this->info("   Найдено доступных мобов: {$availableMobs->count()}");
            foreach ($availableMobs as $mob) {
                $this->info("     - {$mob->name} (ID: {$mob->id}), HP: {$mob->hp}/{$mob->max_hp}");
            }
        } else {
            $this->warn("   ⚠️ Нет обнаруженных игроков для тестирования");
        }

        // Проверяем, есть ли активные метки обелисков
        $this->newLine();
        $this->info("🏛️ ПРОВЕРКА МЕТОК ОБЕЛИСКОВ:");
        
        $getObeliskMarksMethod = $reflection->getMethod('getActiveObeliskMarks');
        $getObeliskMarksMethod->setAccessible(true);
        
        $obeliskMarks = $getObeliskMarksMethod->invoke($job, null);
        $this->info("   Активных меток обелисков: " . count($obeliskMarks));

        // Итоговая диагностика
        $this->newLine();
        $this->info("📊 ИТОГОВАЯ ДИАГНОСТИКА:");
        $this->info("   1. Игрок обнаружен в руднике: " . ($isDetected ? '✅' : '❌'));
        $this->info("   2. Есть активные метки в БД: " . ($marks->count() > 0 ? '✅' : '❌'));
        $this->info("   3. MobAutoAttackJob находит игрока: " . (count($detectedPlayers) > 0 ? '✅' : '❌'));
        $this->info("   4. Есть доступные мобы: " . (isset($availableMobs) && $availableMobs->count() > 0 ? '✅' : '❌'));
        
        if ($isDetected && $marks->count() > 0 && count($detectedPlayers) > 0) {
            $this->info("   ✅ Все условия выполнены - MobAutoAttackJob должен работать!");
        } else {
            $this->warn("   ⚠️ Не все условия выполнены - поэтому MobAutoAttackJob не атакует");
        }

        return 0;
    }
}
